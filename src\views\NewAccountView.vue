<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { newAccountApi } from '../services/newAccountApi.ts';
import MonacoEditor from '../components/MonacoEditor.vue';

// 状态选项
const statusOptions = [
    { value: 'all', label: '所有账号' },
    { value: 'reset_success', label: '重置成功' },
    { value: 'reset_failed', label: '重置失败' },
    { value: 'init_success', label: '验证成功' },
    { value: 'init_failed', label: '验证失败' }
];

const selectedStatus = ref<string>('all');
const selectedDate = ref<string>('');
const accountsText = ref<string>('');
const loading = ref(false);
const showDiff = ref(false);
const originalText = ref('');

// 计算当前按钮文本和显示状态
const buttonConfig = computed(() => {
    switch (selectedStatus.value) {
        case 'all':
            return { text: '保存', show: true, theme: 'primary' };
        case 'reset_failed':
            return { text: '删除重置失败', show: true, theme: 'danger' };
        case 'init_success':
            return { text: '移动到未分配账号', show: true, theme: 'warning' };
        case 'init_failed':
            return { text: '移动到锁定账号', show: true, theme: 'danger' };
        default:
            return { text: '', show: false, theme: 'primary' };
    }
});

const fetchAccounts = async () => {
    try {
        loading.value = true;
        accountsText.value = "";

        let data;
        const params: any = {};

        if (selectedStatus.value !== 'all') {
            params.status = selectedStatus.value;
        }

        if (selectedDate.value) {
            params.date = selectedDate.value;
        }

        data = await newAccountApi.get(params);
        const formattedData = JSON.stringify(data, null, 2);
        accountsText.value = formattedData;
        originalText.value = formattedData;
    } catch (error) {
        MessagePlugin.error('获取账号数据失败');
    } finally {
        loading.value = false;
    }
};

const handleSave = async () => {
    if (selectedStatus.value !== 'all') {
        return;
    }

    try {
        const accounts = JSON.parse(accountsText.value);
        await newAccountApi.update(accounts);
        MessagePlugin.success('保存成功');
        originalText.value = accountsText.value;
    } catch (error: any) {
        MessagePlugin.error(`保存失败: ${error.message || '数据格式错误'}`);
    }
};

const handleDelete = async () => {
    if (selectedStatus.value !== 'reset_failed') {
        return;
    }

    const confirmDialog = DialogPlugin.confirm({
        header: '确认删除',
        body: '确定要删除所有重置失败的账号吗？此操作不可撤销。',
        onConfirm: async () => {
            try {
                await newAccountApi.deleteResetFailed();
                MessagePlugin.success('删除成功');
                await fetchAccounts();
            } catch (error: any) {
                MessagePlugin.error(`删除失败: ${error.message || '未知错误'}`);
            }
            confirmDialog.destroy();
        }
    });
};

const handleMoveToUnassigned = async () => {
    if (selectedStatus.value !== 'init_success') {
        return;
    }

    const confirmDialog = DialogPlugin.confirm({
        header: '确认移动',
        body: '确定要将验证成功的账号移动到未分配账号吗？',
        onConfirm: async () => {
            try {
                await newAccountApi.moveToUnassigned();
                MessagePlugin.success('移动成功');
                await fetchAccounts();
            } catch (error: any) {
                MessagePlugin.error(`移动失败: ${error.message || '未知错误'}`);
            }
            confirmDialog.destroy();
        }
    });
};

const handleMoveToLocked = async () => {
    if (selectedStatus.value !== 'init_failed') {
        return;
    }

    const confirmDialog = DialogPlugin.confirm({
        header: '确认移动',
        body: '确定要将验证失败的账号移动到锁定账号吗？',
        onConfirm: async () => {
            try {
                await newAccountApi.moveToLocked();
                MessagePlugin.success('移动成功');
                await fetchAccounts();
            } catch (error: any) {
                MessagePlugin.error(`移动失败: ${error.message || '未知错误'}`);
            }
            confirmDialog.destroy();
        }
    });
};

const handleButtonClick = () => {
    switch (selectedStatus.value) {
        case 'all':
            handleSave();
            break;
        case 'reset_failed':
            handleDelete();
            break;
        case 'init_success':
            handleMoveToUnassigned();
            break;
        case 'init_failed':
            handleMoveToLocked();
            break;
    }
};

const toggleDiff = () => {
    showDiff.value = !showDiff.value;
};

// 监听状态和日期变化
watch([selectedStatus, selectedDate], () => {
    fetchAccounts();
});

onMounted(() => {
    fetchAccounts();
});
</script>

<template>
    <div class="new-account-container h-full p-2 md:p-5">
        <t-card bordered class="h-full">
            <template #content>
                <div class="flex flex-col h-full">
                    <div class="flex justify-between items-center mb-4 gap-4 flex-wrap">
                        <div class="flex gap-4 items-center flex-wrap">
                            <t-select v-model="selectedStatus" placeholder="选择状态">
                                <t-option v-for="option in statusOptions" :key="option.value"
                                    :value="option.value" :label="option.label" />
                            </t-select>

                            <t-date-picker v-model="selectedDate" placeholder="选择日期"
                                format="YYYY-MM-DD" clearable />
                        </div>

                        <div class="flex gap-2">
                            <t-button variant="outline" @click="toggleDiff">
                                {{ showDiff ? '隐藏对比' : '显示对比' }}
                            </t-button>

                            <t-button v-if="buttonConfig.show"
                                :theme="buttonConfig.theme"
                                @click="handleButtonClick"
                                :loading="loading">
                                {{ buttonConfig.text }}
                            </t-button>
                        </div>
                    </div>

                    <div class="editor-container flex-1">
                        <MonacoEditor v-model:value="accountsText"
                            :original-value="showDiff ? originalText : undefined"
                            language="json"
                            :options="{ tabSize: 2 }" />
                    </div>
                </div>
            </template>
        </t-card>
    </div>
</template>

<style scoped>
.new-account-container {
    width: 100%;
}

:deep(.t-card__body) {
    height: 100%;
}

.editor-container {
    border: 1px solid var(--td-component-border);
}
</style>