import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import moment from 'moment-timezone';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable } from '../db/schema';
import { eq } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
        return handleOptions();
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    if (request.method !== 'POST') {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Method not allowed'
        }), {
            status: 405,
            headers: { 'Content-Type': 'application/json' }
        }));
    }

    try {
        const accounts = await request.json() as any[];
        
        if (!Array.isArray(accounts)) {
            return addCorsHeaders(new Response(JSON.stringify({
                error: 'Invalid data format. Expected an array of accounts.'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
        let updatedCount = 0;
        let notFoundCount = 0;
        const notFoundEmails: string[] = [];

        // 批量更新账号
        for (const account of accounts) {
            if (!account.email) {
                continue; // 跳过没有邮箱的账号
            }

            // 查找现有账号
            const existingAccount = await db.select()
                .from(newaccountsTable)
                .where(eq(newaccountsTable.email, account.email))
                .limit(1);

            if (existingAccount.length === 0) {
                notFoundCount++;
                notFoundEmails.push(account.email);
                continue;
            }

            // 准备更新数据
            const updateData: any = {};
            
            // 只更新提供的字段
            if (account.password !== undefined) {
                updateData.password = account.password;
            }
            if (account.proofEmail !== undefined) {
                updateData.proofEmail = account.proofEmail;
            }
            if (account.resetStatus !== undefined) {
                updateData.resetStatus = account.resetStatus;
                // 如果更新了重置状态，同时更新重置时间
                if (account.resetStatus !== 0) {
                    updateData.resetDatetime = currentTime;
                }
            }
            if (account.resetDatetime !== undefined) {
                updateData.resetDatetime = account.resetDatetime;
            }
            if (account.resetFailMsg !== undefined) {
                updateData.resetFailMsg = account.resetFailMsg;
            }
            if (account.initStatus !== undefined) {
                updateData.initStatus = account.initStatus;
                // 如果更新了验证状态，同时更新验证时间
                if (account.initStatus !== 0) {
                    updateData.initDatetime = currentTime;
                }
            }
            if (account.initDatetime !== undefined) {
                updateData.initDatetime = account.initDatetime;
            }
            if (account.initFailMsg !== undefined) {
                updateData.initFailMsg = account.initFailMsg;
            }

            // 如果有数据需要更新
            if (Object.keys(updateData).length > 0) {
                await db.update(newaccountsTable)
                    .set(updateData)
                    .where(eq(newaccountsTable.email, account.email));
                
                updatedCount++;
            }
        }

        return addCorsHeaders(new Response(JSON.stringify({
            success: true,
            message: 'Accounts updated successfully',
            updatedCount: updatedCount,
            notFoundCount: notFoundCount,
            notFoundEmails: notFoundEmails,
            timestamp: currentTime
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
};
