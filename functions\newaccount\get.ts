import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable } from '../db/schema';
import { eq, and, like } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
        return handleOptions();
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const url = new URL(request.url);
        const status = url.searchParams.get('status');
        const date = url.searchParams.get('date');

        let query = db.select().from(newaccountsTable);
        const conditions = [];

        // 根据状态筛选
        if (status && status !== 'all') {
            switch (status) {
                case 'reset_success':
                    conditions.push(eq(newaccountsTable.resetStatus, 1));
                    break;
                case 'reset_failed':
                    conditions.push(eq(newaccountsTable.resetStatus, 2));
                    break;
                case 'init_success':
                    conditions.push(eq(newaccountsTable.initStatus, 1));
                    break;
                case 'init_failed':
                    conditions.push(eq(newaccountsTable.initStatus, 2));
                    break;
            }
        }

        // 根据日期筛选
        if (date) {
            conditions.push(like(newaccountsTable.createDatetime, `${date}%`));
        }

        // 应用筛选条件
        if (conditions.length > 0) {
            query = query.where(and(...conditions));
        }

        const accounts = await query;

        return addCorsHeaders(new Response(JSON.stringify(accounts), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
};
