import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import moment from 'moment-timezone';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable } from '../db/schema';
import { eq } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
        return handleOptions();
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    if (request.method !== 'POST') {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Method not allowed'
        }), {
            status: 405,
            headers: { 'Content-Type': 'application/json' }
        }));
    }

    try {
        const accounts = await request.json() as any[];
        
        if (!Array.isArray(accounts)) {
            return addCorsHeaders(new Response(JSON.stringify({
                error: 'Invalid data format. Expected an array of accounts.'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

        // 批量插入或更新账号
        for (const account of accounts) {
            if (!account.email || !account.password) {
                continue; // 跳过无效账号
            }

            const accountData = {
                email: account.email,
                password: account.password,
                proofEmail: account.proofEmail || '',
                createDatetime: account.createDatetime || currentTime,
                resetStatus: account.resetStatus || 0,
                resetDatetime: account.resetDatetime || '',
                resetFailMsg: account.resetFailMsg || '',
                initStatus: account.initStatus || 0,
                initDatetime: account.initDatetime || '',
                initFailMsg: account.initFailMsg || ''
            };

            if (account.id) {
                // 更新现有账号
                await db.update(newaccountsTable)
                    .set(accountData)
                    .where(eq(newaccountsTable.id, account.id));
            } else {
                // 插入新账号
                await db.insert(newaccountsTable).values(accountData);
            }
        }

        return addCorsHeaders(new Response(JSON.stringify({
            success: true,
            message: 'Accounts saved successfully'
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
};
