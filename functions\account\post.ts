import { EventContext } from '@cloudflare/workers-types'
import { Env } from '../types'
import { authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { accountsTable } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import moment from 'moment';
import { AccountService } from '../services/accountService';

// 获取需要比较的字段（排除id）
const compareFields = Object.keys(accountsTable).filter(key => key !== 'id');

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const url = new URL(request.url);
        const server = url.searchParams.get('server') || "";
        const newAccounts = await request.json() as any[];

        if (server === "-1" || server === "-2") {
            return addCorsHeaders(new Response(JSON.stringify({
                error: `服务器选择错误`
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        // 验证所有账号的服务器是否匹配
        // for (const account of newAccounts) {
        //     if (account.lock == 0 && account.server && account.server !== server) {
        //         return addCorsHeaders(new Response(JSON.stringify({
        //             error: `账号${account.email}的服务器与请求的服务器不匹配`
        //         }), {
        //             status: 400,
        //             headers: { 'Content-Type': 'application/json' }
        //         }));
        //     }
        // }

        // 获取当前服务器的所有账号
        const existingAccounts = await db.select().from(accountsTable).where(and(
            eq(accountsTable.server, server),
            eq(accountsTable.lock, 0),
        ));
        const today = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss.SSS');
        // 处理每个账号
        for (const account of newAccounts) {
            // 使用 AccountService 创建服务器账号数据
            const accountData = AccountService.createServerAccount(account, server);

            // 特殊处理一些字段
            if (account.createDatetime === "" || !account.createDatetime) {
                accountData.createDatetime = today;
            }

            // 查找是否存在该账号
            const existingAccount = existingAccounts.find(ea =>
                ea.email === account.email
            );

            if (existingAccount) {
                const needUpdate = compareFields.some(key =>
                    existingAccount[key as keyof typeof existingAccount] !== accountData[key as keyof typeof accountData]
                );
                if (needUpdate) {
                    await db.update(accountsTable)
                        .set(accountData)
                        .where(and(
                            eq(accountsTable.email, account.email)
                        ));
                }
            } else {
                // 插入新账号
                await db.insert(accountsTable).values(accountData);
            }
        }

        // 删除不在新列表中的账号
        const newEmails = newAccounts.map(a => a.email);
        for (const existingAccount of existingAccounts) {
            if (!newEmails.includes(existingAccount.email)) {
                await db.delete(accountsTable)
                    .where(and(
                        eq(accountsTable.email, existingAccount.email)
                    ));
            }
        }

        return addCorsHeaders(new Response(JSON.stringify({
            message: 'Accounts saved successfully',
            timestamp: new Date().toISOString()
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: error instanceof Error ? error.message : 'Failed to save accounts'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}
