import { API_BASE_URL, getHeaders, handleResponse } from './util';

export const newAccountApi = {
    // 获取新账号数据
    async get(params: { status?: string; date?: string } = {}): Promise<any> {
        const url = new URL(`${API_BASE_URL}/newaccount/get`);
        
        if (params.status) {
            url.searchParams.set('status', params.status);
        }
        
        if (params.date) {
            url.searchParams.set('date', params.date);
        }
        
        const response = await fetch(url.toString(), {
            headers: getHeaders()
        });
        return handleResponse(response);
    },

    // 更新新账号数据
    async update(accounts: any): Promise<any> {
        const response = await fetch(
            `${API_BASE_URL}/newaccount/post`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify(accounts)
            }
        );
        return handleResponse(response);
    },

    // 删除重置失败的账号
    async deleteResetFailed(): Promise<any> {
        const response = await fetch(
            `${API_BASE_URL}/newaccount/delete-reset-failed`,
            {
                headers: getHeaders(),
                method: 'DELETE'
            }
        );
        return handleResponse(response);
    },

    // 将验证成功的账号移动到未分配账号
    async moveToUnassigned(): Promise<any> {
        const response = await fetch(
            `${API_BASE_URL}/newaccount/move-to-unassigned`,
            {
                headers: getHeaders(),
                method: 'POST'
            }
        );
        return handleResponse(response);
    },

    // 将验证失败的账号移动到锁定账号
    async moveToLocked(): Promise<any> {
        const response = await fetch(
            `${API_BASE_URL}/newaccount/move-to-locked`,
            {
                headers: getHeaders(),
                method: 'POST'
            }
        );
        return handleResponse(response);
    }
};
