import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import moment from 'moment-timezone';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable, accountsTable } from '../db/schema';
import { eq } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
        return handleOptions();
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    if (request.method !== 'POST') {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Method not allowed'
        }), {
            status: 405,
            headers: { 'Content-Type': 'application/json' }
        }));
    }

    try {
        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

        // 获取所有验证成功的账号 (initStatus = 1)
        const successAccounts = await db.select()
            .from(newaccountsTable)
            .where(eq(newaccountsTable.initStatus, 1));

        if (successAccounts.length === 0) {
            return addCorsHeaders(new Response(JSON.stringify({
                success: true,
                message: 'No accounts to move'
            }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        // 将账号移动到 accounts 表（未分配账号，server = ""）
        for (const account of successAccounts) {
            await db.insert(accountsTable).values({
                email: account.email,
                password: account.password,
                proofEmail: account.proofEmail || '',
                server: '', // 未分配账号
                score: 0,
                weight: 0,
                disabled: 0,
                lock: 0,
                pcSearchPointProgress: 0,
                mobileSearchPointProgress: 0,
                pcSearchCount: 0,
                mobileSearchCount: 0,
                executions: 0,
                createDatetime: account.createDatetime || currentTime,
                updateDatetime: currentTime,
                maxDailyExecutionLimit: -1,
                maxSearchPerRequest: -1,
                maxDailySearchLimit: -1,
                maxReadPerRequest: -1,
                maxDailyReadLimit: -1,
                ignoreDistributionCycleDays: 0,
                onlyLogin: 0
            });
        }

        // 从 newaccounts 表中删除已移动的账号
        await db.delete(newaccountsTable)
            .where(eq(newaccountsTable.initStatus, 1));

        return addCorsHeaders(new Response(JSON.stringify({
            success: true,
            message: `${successAccounts.length} accounts moved to unassigned successfully`
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
};
