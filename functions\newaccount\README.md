# NewAccount API 使用说明

## API 端点

### 1. GET /newaccount/get
获取新账号数据，支持状态筛选和排序

**参数：**
- `status` (可选): 筛选状态
  - `all`: 所有账号
  - `reset_success`: 重置成功 (按 resetDatetime 降序)
  - `reset_failed`: 重置失败 (按 resetDatetime 降序)
  - `init_success`: 验证成功 (按 initDatetime 降序)
  - `init_failed`: 验证失败 (按 initDatetime 降序)

### 2. POST /newaccount/post
创建或更新新账号数据（完整替换）

### 3. POST /newaccount/update
根据 email 更新现有账号数据（部分更新）

**请求体示例：**
```json
[
  {
    "email": "<EMAIL>",
    "resetStatus": 1,
    "resetFailMsg": ""
  },
  {
    "email": "<EMAIL>", 
    "initStatus": 2,
    "initFailMsg": "验证失败：手机号码无效"
  }
]
```

**响应示例：**
```json
{
  "success": true,
  "message": "Accounts updated successfully",
  "updatedCount": 2,
  "notFoundCount": 0,
  "notFoundEmails": [],
  "timestamp": "2025-07-31 10:30:00"
}
```

### 4. DELETE /newaccount/delete-reset-failed
删除所有重置失败的账号

### 5. POST /newaccount/move-to-unassigned
将验证成功的账号移动到未分配账号

### 6. POST /newaccount/move-to-locked
将验证失败的账号移动到锁定账号

## 字段说明

### 可更新字段：
- `email`: 邮箱地址（必需，用于查找）
- `password`: 密码
- `proofEmail`: 备用邮箱
- `resetStatus`: 重置状态 (0=默认, 1=成功, 2=失败)
- `resetDatetime`: 重置时间
- `resetFailMsg`: 重置失败消息
- `initStatus`: 验证状态 (0=默认, 1=成功, 2=失败)
- `initDatetime`: 验证时间
- `initFailMsg`: 验证失败消息

### 自动处理：
- 当 `resetStatus` 更新为非0值时，会自动设置 `resetDatetime` 为当前时间
- 当 `initStatus` 更新为非0值时，会自动设置 `initDatetime` 为当前时间

## 前端调用示例

```typescript
import { newAccountApi } from '../services/newAccountApi';

// 更新账号状态
const updateAccounts = async () => {
  try {
    const accounts = [
      {
        email: "<EMAIL>",
        resetStatus: 1  // 重置成功
      },
      {
        email: "<EMAIL>",
        initStatus: 2,  // 验证失败
        initFailMsg: "手机验证失败"
      }
    ];
    
    const result = await newAccountApi.updateByEmail(accounts);
    console.log('更新结果:', result);
  } catch (error) {
    console.error('更新失败:', error);
  }
};
```
